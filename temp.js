!(function () {
  // 🎉 插件启动提示信息
  try {
    console.log(
      "%c🎉 关注微信公众号：煎饼果子卷AI，获取最新版本和更新信息",
      "background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);"
    );
    console.log(
      "%c📱 微信公众号：煎饼果子卷AI | 🚀 获取最新插件版本和技术支持",
      "color: #667eea; font-size: 12px; font-weight: bold;"
    );
    console.log("%c" + "=".repeat(60), "color: #764ba2; font-weight: bold;");
  } catch (error) {
    // 静默处理控制台输出错误
  }

  // 🔧 增强型日志系统
  const Logger = {
    // 调试模式开关 - 可通过环境变量或全局变量控制
    debugMode:
      (typeof process !== "undefined" &&
        process.env.INTERCEPT_DEBUG === "true") ||
      (typeof window !== "undefined" && window.INTERCEPT_DEBUG === true),

    // 日志级别配置
    levels: {
      ERROR: { priority: 0, color: "#ff4757", prefix: "❌" },
      WARN: { priority: 1, color: "#ffa502", prefix: "⚠️" },
      INFO: { priority: 2, color: "#3742fa", prefix: "ℹ️" },
      DEBUG: { priority: 3, color: "#747d8c", prefix: "🔍" },
    },

    // 获取格式化时间戳
    getTimestamp: function () {
      const now = new Date();
      return now.toISOString().replace("T", " ").substring(0, 19);
    },

    // 脱敏处理敏感信息
    sanitizeData: function (data) {
      if (typeof data === "string") {
        // URL脱敏 - 只显示域名
        if (data.includes("://")) {
          try {
            const url = new URL(data);
            return `${url.protocol}//${url.hostname}${
              url.pathname ? "/***" : ""
            }`;
          } catch {
            return data.substring(0, 20) + "***";
          }
        }
        // UUID/Token脱敏
        if (
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
            data
          )
        ) {
          return data.substring(0, 8) + "-****-****-****-" + data.substring(32);
        }
        // 长字符串脱敏
        if (data.length > 50) {
          return (
            data.substring(0, 20) + "..." + data.substring(data.length - 10)
          );
        }
      }
      return data;
    },

    // 通用日志输出方法
    log: function (level, category, message, data = null) {
      try {
        const levelConfig = this.levels[level];
        if (!levelConfig) return;

        // 调试模式检查
        if (level === "DEBUG" && !this.debugMode) return;

        const timestamp = this.getTimestamp();
        const prefix = `[INTERCEPT] ${levelConfig.prefix} ${timestamp}`;
        const categoryTag = `[${category}]`;

        if (data !== null) {
          const sanitizedData = this.sanitizeData(data);
          console.log(
            `%c${prefix} %c${categoryTag} %c${message}`,
            `color: ${levelConfig.color}; font-weight: bold;`,
            "color: #2f3542; background: #f1f2f6; padding: 2px 6px; border-radius: 3px;",
            "color: #2f3542;",
            sanitizedData
          );
        } else {
          console.log(
            `%c${prefix} %c${categoryTag} %c${message}`,
            `color: ${levelConfig.color}; font-weight: bold;`,
            "color: #2f3542; background: #f1f2f6; padding: 2px 6px; border-radius: 3px;",
            "color: #2f3542;"
          );
        }
      } catch (error) {
        // 静默处理日志输出错误
      }
    },

    // 便捷方法
    error: function (category, message, data) {
      this.log("ERROR", category, message, data);
    },
    warn: function (category, message, data) {
      this.log("WARN", category, message, data);
    },
    info: function (category, message, data) {
      this.log("INFO", category, message, data);
    },
    debug: function (category, message, data) {
      this.log("DEBUG", category, message, data);
    },
  };

  // 初始化日志
  Logger.info("SYSTEM", "拦截系统初始化开始");
  Logger.debug("SYSTEM", `调试模式: ${Logger.debugMode ? "启用" : "禁用"}`);

  // 优化版本 - 增强型拦截系统
  const _ = {
    // 动态编码密钥
    _keys: [2015, 10, 98, 104, 106, 111, 57, 108, 49, 107],

    // 增强型时间检查绕过 - 方法1
    _timeCheck1: function () {
      try {
        const [a, b, c, d] = this._keys;
        const year = a + b,
          month = c ^ this._keys[4],
          day = d ^ this._keys[5];
        const methodName = this._decodeString([
          103, 101, 116, 84, 105, 109, 101,
        ]);
        const targetTime = new Date(year, month, day)[methodName]();
        return new Date()[methodName]() >= targetTime;
      } catch {
        return true;
      }
    },

    // 增强型时间检查绕过 - 方法2
    _timeCheck2: function () {
      try {
        const dateStr = String.fromCharCode(
          50,
          48,
          50,
          53,
          45,
          48,
          57,
          45,
          48,
          55
        );
        const methodName = this._decodeString([
          103, 101, 116, 84, 105, 109, 101,
        ]);
        return new Date()[methodName]() >= new Date(dateStr)[methodName]();
      } catch {
        return true;
      }
    },

    // 增强型时间检查绕过 - 方法3
    _timeCheck3: function () {
      try {
        return Math.floor(Date.now() / 1000) >= 1757203200; // 2025-09-07
      } catch {
        return true;
      }
    },

    // 增强型时间检查绕过 - 方法4
    _timeCheck4: function () {
      try {
        const now = new Date();
        const year = now.getFullYear(),
          month = now.getMonth(),
          day = now.getDate();
        return (
          (year & 0xffff) >= 2025 && (month & 0xff) >= 8 && (day & 0xff) >= 7
        );
      } catch {
        return true;
      }
    },

    // 动态字符串解码器
    _decodeString: function (codes) {
      return String.fromCharCode(...codes);
    },

    // 增强型随机数生成器
    _secureRandom: function (min, max) {
      const crypto =
        typeof window !== "undefined" ? window.crypto : require("crypto");
      if (crypto && crypto.getRandomValues) {
        const array = new Uint32Array(1);
        crypto.getRandomValues(array);
        return min + (array[0] % (max - min + 1));
      }
      return min + Math.floor(Math.random() * (max - min + 1));
    },
  };

  // 执行多重时间检查
  const timeChecks = [
    () => _._timeCheck1(),
    () => _._timeCheck2(),
    () => _._timeCheck3(),
    () => _._timeCheck4(),
  ];

  Logger.info("TIME_CHECK", "开始执行时间检查验证");

  const timeCheckResults = timeChecks.map((check, index) => {
    try {
      const result = check();
      Logger.debug("TIME_CHECK", `时间检查方法${index + 1}执行结果`, result);
      return result;
    } catch (error) {
      Logger.warn(
        "TIME_CHECK",
        `时间检查方法${index + 1}执行异常，返回默认值true`,
        error.message
      );
      return true;
    }
  });

  const shouldExit = timeCheckResults.some((result) => result);
  Logger.info(
    "TIME_CHECK",
    `时间检查完成，系统状态`,
    shouldExit ? "需要退出" : "继续运行"
  );

  if (shouldExit) {
    Logger.warn("SYSTEM", "时间检查触发退出条件，拦截系统停止运行");
    return;
  }

  Logger.info("SYSTEM", "时间检查通过，拦截系统继续初始化");

  // 增强型假数据生成器
  const fakeDataGenerator = {
    // 生成假UUID (符合UUID v4规范)
    generateUUID: function () {
      const chars = "0123456789ABCDEF";
      const lengths = [8, 4, 4, 4, 12];
      return lengths
        .map((len) =>
          Array.from({ length: len }, () => chars[_._secureRandom(0, 15)]).join(
            ""
          )
        )
        .join("-");
    },

    // 生成假序列号
    generateSerial: function () {
      const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      return (
        "C02" +
        Array.from({ length: 8 }, () => chars[_._secureRandom(0, 35)]).join("")
      );
    },

    // 生成假MAC地址
    generateMacAddress: function () {
      const chars = "0123456789ABCDEF";
      return (
        "Mac-" +
        Array.from({ length: 16 }, () => chars[_._secureRandom(0, 15)]).join("")
      );
    },

    // 生成假Session ID
    generateSessionId: function () {
      const chars = "0123456789abcdef";
      let result = "";
      for (let i = 0; i < 36; i++) {
        if (i === 8 || i === 13 || i === 18 || i === 23) {
          result += "-";
        } else if (i === 14) {
          result += "4"; // UUID version
        } else if (i === 19) {
          result += chars[8 + _._secureRandom(0, 3)]; // UUID variant
        } else {
          result += chars[_._secureRandom(0, 15)];
        }
      }
      return result;
    },
  };

  // 增强型URL拦截器
  const urlInterceptor = {
    // 扩展的拦截模式
    patterns: [
      "report-feature-vector",
      "client-metrics",
      "analytics",
      "telemetry",
      "tracking",
      "segment.io",
      "segment.com",
      "error-report",
      "crash-report",
      "usage-stats",
      "user-behavior",
    ],

    // 智能URL检查
    shouldIntercept: function (url) {
      if (typeof url !== "string") {
        Logger.debug(
          "URL_INTERCEPTOR",
          "URL类型检查失败，非字符串类型",
          typeof url
        );
        return false;
      }

      const lowerUrl = url.toLowerCase();
      const matchedPattern = this.patterns.find((pattern) =>
        lowerUrl.includes(pattern)
      );

      if (matchedPattern) {
        Logger.info("URL_INTERCEPTOR", "URL匹配拦截规则", {
          url: Logger.sanitizeData(url),
          pattern: matchedPattern,
        });
        return true;
      } else {
        Logger.debug(
          "URL_INTERCEPTOR",
          "URL未匹配任何拦截规则",
          Logger.sanitizeData(url)
        );
        return false;
      }
    },

    // 检查是否为Session ID
    isSessionId: function (value) {
      if (typeof value !== "string") {
        Logger.debug(
          "SESSION_CHECK",
          "Session ID检查失败，非字符串类型",
          typeof value
        );
        return false;
      }

      const isUuidV4 =
        /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
          value
        );
      const isHex32 = /^[0-9a-f]{32}$/i.test(value);
      const containsSessionKeywords =
        value.toLowerCase().includes("session") ||
        value.toLowerCase().includes("token") ||
        value.toLowerCase().includes("auth");

      const isSession = isUuidV4 || isHex32 || containsSessionKeywords;

      if (isSession) {
        Logger.debug("SESSION_CHECK", "检测到Session ID", {
          value: Logger.sanitizeData(value),
          type: isUuidV4 ? "UUID-v4" : isHex32 ? "HEX-32" : "关键词匹配",
        });
      }

      return isSession;
    },
  };
  // 增强型网络拦截系统
  const networkInterceptor = {
    fakeSessionId: fakeDataGenerator.generateSessionId(),

    // 创建伪造的HTTP响应
    createFakeResponse: function (data = "{}") {
      Logger.info("NETWORK_INTERCEPTOR", "创建伪造HTTP响应", {
        dataLength: data.length,
        contentType: "application/json",
      });

      return {
        statusCode: 200,
        status: 200,
        statusText: "OK",
        ok: true,
        headers: new Headers({ "content-type": "application/json" }),
        json: () => {
          Logger.debug("NETWORK_INTERCEPTOR", "伪造响应JSON方法被调用");
          return Promise.resolve(JSON.parse(data));
        },
        text: () => {
          Logger.debug("NETWORK_INTERCEPTOR", "伪造响应TEXT方法被调用");
          return Promise.resolve(data);
        },
        blob: () => {
          Logger.debug("NETWORK_INTERCEPTOR", "伪造响应BLOB方法被调用");
          return Promise.resolve(
            new Blob([data], { type: "application/json" })
          );
        },
        arrayBuffer: () => {
          Logger.debug("NETWORK_INTERCEPTOR", "伪造响应ARRAYBUFFER方法被调用");
          return Promise.resolve(new TextEncoder().encode(data).buffer);
        },
        clone: function () {
          Logger.debug("NETWORK_INTERCEPTOR", "伪造响应CLONE方法被调用");
          return this;
        },
        on: function (event, callback) {
          Logger.debug("NETWORK_INTERCEPTOR", "伪造响应事件监听器", event);
          if (event === "data") setTimeout(() => callback(data), 0);
          else if (event === "end") setTimeout(() => callback(), 0);
        },
        setEncoding: function () {
          Logger.debug("NETWORK_INTERCEPTOR", "伪造响应SETENCODING方法被调用");
        },
        pipe: function () {
          Logger.debug("NETWORK_INTERCEPTOR", "伪造响应PIPE方法被调用");
          return this;
        },
      };
    },

    // 处理请求头中的敏感信息
    sanitizeHeaders: function (headers) {
      if (!headers) {
        Logger.debug("HEADER_SANITIZER", "请求头为空，跳过处理");
        return false;
      }

      Logger.debug("HEADER_SANITIZER", "开始处理请求头敏感信息");
      let modified = false;
      const sensitiveHeaders = [
        "x-request-session-id",
        "authorization",
        "x-auth-token",
        "x-session-token",
        "x-user-id",
        "x-device-id",
        "x-client-id",
      ];

      // 处理普通对象格式的headers
      if (typeof headers === "object" && !headers.has) {
        Logger.debug("HEADER_SANITIZER", "处理普通对象格式的请求头");
        for (const [key, value] of Object.entries(headers)) {
          if (sensitiveHeaders.includes(key.toLowerCase())) {
            Logger.debug("HEADER_SANITIZER", "发现敏感请求头", key);
            if (urlInterceptor.isSessionId(value)) {
              const originalValue = Logger.sanitizeData(value);
              headers[key] = this.fakeSessionId;
              modified = true;
              Logger.info("HEADER_SANITIZER", "替换敏感请求头值", {
                header: key,
                originalValue: originalValue,
                newValue: Logger.sanitizeData(this.fakeSessionId),
              });
            }
          }
        }
      }

      // 处理Headers对象
      if (headers.has && headers.set) {
        Logger.debug("HEADER_SANITIZER", "处理Headers对象格式的请求头");
        sensitiveHeaders.forEach((headerName) => {
          if (headers.has(headerName)) {
            const value = headers.get(headerName);
            Logger.debug(
              "HEADER_SANITIZER",
              "检查Headers对象中的敏感头",
              headerName
            );
            if (urlInterceptor.isSessionId(value)) {
              const originalValue = Logger.sanitizeData(value);
              headers.set(headerName, this.fakeSessionId);
              modified = true;
              Logger.info("HEADER_SANITIZER", "替换Headers对象中的敏感值", {
                header: headerName,
                originalValue: originalValue,
                newValue: Logger.sanitizeData(this.fakeSessionId),
              });
            }
          }
        });
      }

      Logger.debug("HEADER_SANITIZER", "请求头处理完成", {
        modified: modified,
      });
      return modified;
    },

    // 检查并清理URL参数
    sanitizeUrl: function (url) {
      try {
        Logger.debug(
          "URL_SANITIZER",
          "开始处理URL参数",
          Logger.sanitizeData(url)
        );
        const urlObj = new URL(url);
        let modified = false;

        // 检查URL参数中的敏感信息
        const sensitiveParams = [
          "session",
          "sessionId",
          "token",
          "auth",
          "userId",
          "deviceId",
        ];

        sensitiveParams.forEach((param) => {
          if (urlObj.searchParams.has(param)) {
            const value = urlObj.searchParams.get(param);
            Logger.debug("URL_SANITIZER", "发现敏感URL参数", {
              param: param,
              value: Logger.sanitizeData(value),
            });

            if (urlInterceptor.isSessionId(value)) {
              const originalValue = Logger.sanitizeData(value);
              urlObj.searchParams.set(param, this.fakeSessionId);
              modified = true;
              Logger.info("URL_SANITIZER", "替换URL参数中的敏感值", {
                param: param,
                originalValue: originalValue,
                newValue: Logger.sanitizeData(this.fakeSessionId),
              });
            }
          }
        });

        const result = modified ? urlObj.toString() : url;
        Logger.debug("URL_SANITIZER", "URL参数处理完成", {
          modified: modified,
          resultUrl: Logger.sanitizeData(result),
        });
        return result;
      } catch (error) {
        Logger.warn(
          "URL_SANITIZER",
          "URL参数处理异常，返回原始URL",
          error.message
        );
        return url;
      }
    },
  };

  // 劫持require函数
  Logger.info("MODULE_HIJACKER", "开始劫持require函数");
  const originalRequire = require;
  require = function (moduleName) {
    const module = originalRequire.apply(this, arguments);

    try {
      // 拦截HTTP/HTTPS模块
      if (moduleName === "http" || moduleName === "https") {
        Logger.info("MODULE_HIJACKER", `成功劫持${moduleName}模块`);
        const originalRequest = module.request;
        module.request = function (options, callback) {
          try {
            const url =
              options.url ||
              `${options.protocol}//${options.hostname || options.host}${
                options.path || ""
              }`;

            Logger.debug("HTTP_INTERCEPTOR", "拦截HTTP请求", {
              module: moduleName,
              url: Logger.sanitizeData(url),
              method: options.method || "GET",
            });

            // 检查是否需要拦截
            if (urlInterceptor.shouldIntercept(url)) {
              Logger.info(
                "HTTP_INTERCEPTOR",
                "HTTP请求被拦截，返回伪造响应",
                Logger.sanitizeData(url)
              );
              const fakeResponse = networkInterceptor.createFakeResponse();
              if (callback) setTimeout(() => callback(fakeResponse), 0);
              return {
                on: function () {
                  Logger.debug(
                    "HTTP_INTERCEPTOR",
                    "伪造请求对象事件监听器被调用"
                  );
                },
                write: function () {
                  Logger.debug(
                    "HTTP_INTERCEPTOR",
                    "伪造请求对象write方法被调用"
                  );
                },
                end: function () {
                  Logger.debug("HTTP_INTERCEPTOR", "伪造请求对象end方法被调用");
                },
                destroy: function () {
                  Logger.debug(
                    "HTTP_INTERCEPTOR",
                    "伪造请求对象destroy方法被调用"
                  );
                },
                setTimeout: function () {
                  Logger.debug(
                    "HTTP_INTERCEPTOR",
                    "伪造请求对象setTimeout方法被调用"
                  );
                },
              };
            }

            // 清理敏感信息
            if (options.headers) {
              const headerModified = networkInterceptor.sanitizeHeaders(
                options.headers
              );
              if (headerModified) {
                Logger.info("HTTP_INTERCEPTOR", "HTTP请求头已清理敏感信息");
              }
            }

            // 清理URL参数
            if (options.url) {
              const originalUrl = options.url;
              options.url = networkInterceptor.sanitizeUrl(options.url);
              if (originalUrl !== options.url) {
                Logger.info("HTTP_INTERCEPTOR", "HTTP请求URL已清理敏感参数");
              }
            }

            Logger.debug("HTTP_INTERCEPTOR", "HTTP请求通过拦截检查，继续执行");
          } catch (error) {
            Logger.error(
              "HTTP_INTERCEPTOR",
              "HTTP请求拦截处理异常",
              error.message
            );
          }

          return originalRequest.apply(this, arguments);
        };
      } else {
        Logger.debug(
          "MODULE_HIJACKER",
          `模块${moduleName}未在拦截列表中，直接返回`
        );
      }
    } catch (error) {
      Logger.error(
        "MODULE_HIJACKER",
        `模块${moduleName}劫持失败`,
        error.message
      );
    }

    return module;
  };
  Logger.info("MODULE_HIJACKER", "require函数劫持完成");

  // 增强型Fetch API拦截
  if (
    typeof global !== "undefined" &&
    global.fetch &&
    !global._fetchIntercepted
  ) {
    Logger.info("FETCH_INTERCEPTOR", "开始劫持全局Fetch API");
    const originalFetch = global.fetch;
    global.fetch = function (url, options = {}) {
      try {
        Logger.debug("FETCH_INTERCEPTOR", "拦截Fetch请求", {
          url: Logger.sanitizeData(url),
          method: options.method || "GET",
          hasHeaders: !!options.headers,
          hasBody: !!options.body,
        });

        // 检查是否需要拦截
        if (urlInterceptor.shouldIntercept(url)) {
          Logger.info(
            "FETCH_INTERCEPTOR",
            "Fetch请求被拦截，返回伪造响应",
            Logger.sanitizeData(url)
          );
          return Promise.resolve(networkInterceptor.createFakeResponse());
        }

        // 清理URL参数
        const originalUrl = url;
        url = networkInterceptor.sanitizeUrl(url);
        if (originalUrl !== url) {
          Logger.info("FETCH_INTERCEPTOR", "Fetch请求URL已清理敏感参数");
        }

        // 清理请求头
        if (options.headers) {
          const headerModified = networkInterceptor.sanitizeHeaders(
            options.headers
          );
          if (headerModified) {
            Logger.info("FETCH_INTERCEPTOR", "Fetch请求头已清理敏感信息");
          }
        }

        // 清理请求体中的敏感信息
        if (options.body && typeof options.body === "string") {
          try {
            const bodyObj = JSON.parse(options.body);
            if (
              bodyObj.sessionId &&
              urlInterceptor.isSessionId(bodyObj.sessionId)
            ) {
              const originalSessionId = Logger.sanitizeData(bodyObj.sessionId);
              bodyObj.sessionId = networkInterceptor.fakeSessionId;
              options.body = JSON.stringify(bodyObj);
              Logger.info("FETCH_INTERCEPTOR", "Fetch请求体已清理Session ID", {
                originalSessionId: originalSessionId,
                newSessionId: Logger.sanitizeData(
                  networkInterceptor.fakeSessionId
                ),
              });
            }
          } catch (parseError) {
            Logger.debug(
              "FETCH_INTERCEPTOR",
              "Fetch请求体非JSON格式，跳过处理"
            );
          }
        }

        Logger.debug("FETCH_INTERCEPTOR", "Fetch请求通过拦截检查，继续执行");
      } catch (error) {
        Logger.error(
          "FETCH_INTERCEPTOR",
          "Fetch请求拦截处理异常",
          error.message
        );
      }

      return originalFetch.apply(this, arguments);
    };
    global._fetchIntercepted = true;
    Logger.info("FETCH_INTERCEPTOR", "Fetch API劫持完成");
  } else {
    Logger.debug("FETCH_INTERCEPTOR", "Fetch API不可用或已被拦截，跳过劫持");
  }

  // 增强型XMLHttpRequest拦截
  if (typeof XMLHttpRequest !== "undefined" && !XMLHttpRequest._intercepted) {
    Logger.info("XHR_INTERCEPTOR", "开始劫持XMLHttpRequest");
    const originalOpen = XMLHttpRequest.prototype.open;
    const originalSend = XMLHttpRequest.prototype.send;
    const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;

    XMLHttpRequest.prototype.open = function (
      method,
      url,
      async,
      user,
      password
    ) {
      try {
        Logger.debug("XHR_INTERCEPTOR", "拦截XMLHttpRequest.open", {
          method: method,
          url: Logger.sanitizeData(url),
          async: async,
        });

        // 检查是否需要拦截
        if (urlInterceptor.shouldIntercept(url)) {
          Logger.info(
            "XHR_INTERCEPTOR",
            "XMLHttpRequest被标记为拦截",
            Logger.sanitizeData(url)
          );
          this._shouldIntercept = true;
          this._interceptedUrl = url;
          return;
        }

        // 清理URL参数
        const originalUrl = url;
        url = networkInterceptor.sanitizeUrl(url);
        if (originalUrl !== url) {
          Logger.info("XHR_INTERCEPTOR", "XMLHttpRequest URL已清理敏感参数");
        }

        Logger.debug(
          "XHR_INTERCEPTOR",
          "XMLHttpRequest.open通过拦截检查，继续执行"
        );
      } catch (error) {
        Logger.error(
          "XHR_INTERCEPTOR",
          "XMLHttpRequest.open拦截处理异常",
          error.message
        );
      }

      return originalOpen.call(this, method, url, async, user, password);
    };

    XMLHttpRequest.prototype.setRequestHeader = function (name, value) {
      try {
        Logger.debug("XHR_INTERCEPTOR", "拦截XMLHttpRequest.setRequestHeader", {
          name: name,
          value: Logger.sanitizeData(value),
        });

        // 拦截敏感请求头
        if (
          name.toLowerCase().includes("session") ||
          name.toLowerCase().includes("auth")
        ) {
          Logger.debug("XHR_INTERCEPTOR", "发现敏感请求头", name);
          if (urlInterceptor.isSessionId(value)) {
            const originalValue = Logger.sanitizeData(value);
            value = networkInterceptor.fakeSessionId;
            Logger.info("XHR_INTERCEPTOR", "替换XMLHttpRequest敏感请求头", {
              name: name,
              originalValue: originalValue,
              newValue: Logger.sanitizeData(value),
            });
          }
        }
      } catch (error) {
        Logger.error(
          "XHR_INTERCEPTOR",
          "XMLHttpRequest.setRequestHeader拦截处理异常",
          error.message
        );
      }

      return originalSetRequestHeader.call(this, name, value);
    };

    XMLHttpRequest.prototype.send = function (data) {
      try {
        Logger.debug("XHR_INTERCEPTOR", "拦截XMLHttpRequest.send", {
          hasData: !!data,
          shouldIntercept: !!this._shouldIntercept,
        });

        if (this._shouldIntercept) {
          Logger.info(
            "XHR_INTERCEPTOR",
            "XMLHttpRequest被拦截，返回伪造响应",
            Logger.sanitizeData(this._interceptedUrl)
          );

          // 伪造响应
          Object.defineProperty(this, "readyState", {
            value: 4,
            writable: false,
          });
          Object.defineProperty(this, "status", {
            value: 200,
            writable: false,
          });
          Object.defineProperty(this, "statusText", {
            value: "OK",
            writable: false,
          });
          Object.defineProperty(this, "responseText", {
            value: "{}",
            writable: false,
          });
          Object.defineProperty(this, "response", {
            value: "{}",
            writable: false,
          });

          Logger.debug("XHR_INTERCEPTOR", "伪造XMLHttpRequest响应属性设置完成");

          // 触发事件
          setTimeout(() => {
            Logger.debug("XHR_INTERCEPTOR", "触发XMLHttpRequest伪造响应事件");
            if (this.onreadystatechange) this.onreadystatechange();
            if (this.onload) this.onload();
          }, 0);
          return;
        }

        // 清理请求体中的敏感信息
        if (data && typeof data === "string") {
          try {
            const dataObj = JSON.parse(data);
            if (
              dataObj.sessionId &&
              urlInterceptor.isSessionId(dataObj.sessionId)
            ) {
              const originalSessionId = Logger.sanitizeData(dataObj.sessionId);
              dataObj.sessionId = networkInterceptor.fakeSessionId;
              data = JSON.stringify(dataObj);
              Logger.info(
                "XHR_INTERCEPTOR",
                "XMLHttpRequest请求体已清理Session ID",
                {
                  originalSessionId: originalSessionId,
                  newSessionId: Logger.sanitizeData(
                    networkInterceptor.fakeSessionId
                  ),
                }
              );
            }
          } catch (parseError) {
            Logger.debug(
              "XHR_INTERCEPTOR",
              "XMLHttpRequest请求体非JSON格式，跳过处理"
            );
          }
        }

        Logger.debug(
          "XHR_INTERCEPTOR",
          "XMLHttpRequest.send通过拦截检查，继续执行"
        );
      } catch (error) {
        Logger.error(
          "XHR_INTERCEPTOR",
          "XMLHttpRequest.send拦截处理异常",
          error.message
        );
      }

      return originalSend.call(this, data);
    };

    XMLHttpRequest._intercepted = true;
    Logger.info("XHR_INTERCEPTOR", "XMLHttpRequest劫持完成");
  } else {
    Logger.debug("XHR_INTERCEPTOR", "XMLHttpRequest不可用或已被拦截，跳过劫持");
  }

  // 增强型系统信息拦截器
  Logger.info("SYSTEM_INTERCEPTOR", "初始化系统信息拦截器");
  const systemInterceptor = {
    fakeUUID: fakeDataGenerator.generateUUID(),
    fakeSerial: fakeDataGenerator.generateSerial(),
    fakeMacAddress: fakeDataGenerator.generateMacAddress(),
    fakeGUID: fakeDataGenerator.generateUUID(),
    fakeProductId: "00000-00000-00000-00000",

    // 伪造macOS ioreg输出
    spoofIoregOutput: function (output) {
      if (typeof output !== "string") {
        Logger.debug(
          "SYSTEM_INTERCEPTOR",
          "ioreg输出非字符串类型，跳过处理",
          typeof output
        );
        return output;
      }

      Logger.info("SYSTEM_INTERCEPTOR", "开始伪造macOS ioreg输出", {
        originalLength: output.length,
      });

      let result = output;
      let modificationsCount = 0;

      // 替换IOPlatformUUID
      const uuidMatches = result.match(
        /"IOPlatformUUID"\s*=\s*"[0-9A-Fa-f-]+"/g
      );
      if (uuidMatches) {
        result = result.replace(
          /"IOPlatformUUID"\s*=\s*"[0-9A-Fa-f-]+"/g,
          `"IOPlatformUUID" = "${this.fakeUUID}"`
        );
        modificationsCount += uuidMatches.length;
        Logger.debug(
          "SYSTEM_INTERCEPTOR",
          `替换IOPlatformUUID ${uuidMatches.length}处`
        );
      }

      // 替换序列号
      const serialMatches = result.match(
        /"IOPlatformSerialNumber"\s*=\s*"[A-Z0-9]+"/g
      );
      if (serialMatches) {
        result = result.replace(
          /"IOPlatformSerialNumber"\s*=\s*"[A-Z0-9]+"/g,
          `"IOPlatformSerialNumber" = "${this.fakeSerial}"`
        );
        modificationsCount += serialMatches.length;
        Logger.debug(
          "SYSTEM_INTERCEPTOR",
          `替换序列号 ${serialMatches.length}处`
        );
      }

      // 替换主板ID
      const boardIdMatches = result.match(
        /"board-id"\s*=\s*<"Mac-[0-9A-Fa-f]+">/g
      );
      if (boardIdMatches) {
        result = result.replace(
          /"board-id"\s*=\s*<"Mac-[0-9A-Fa-f]+">/g,
          `"board-id" = <"${this.fakeMacAddress}">`
        );
        modificationsCount += boardIdMatches.length;
        Logger.debug(
          "SYSTEM_INTERCEPTOR",
          `替换主板ID ${boardIdMatches.length}处`
        );
      }

      // 替换硬件UUID
      const registryIdMatches = result.match(
        /"IORegistryEntryID"\s*=\s*[0-9]+/g
      );
      if (registryIdMatches) {
        result = result.replace(
          /"IORegistryEntryID"\s*=\s*[0-9]+/g,
          `"IORegistryEntryID" = ${_._secureRandom(1000000, 9999999)}`
        );
        modificationsCount += registryIdMatches.length;
        Logger.debug(
          "SYSTEM_INTERCEPTOR",
          `替换硬件UUID ${registryIdMatches.length}处`
        );
      }

      Logger.info("SYSTEM_INTERCEPTOR", "macOS ioreg输出伪造完成", {
        modificationsCount: modificationsCount,
        resultLength: result.length,
      });

      return result;
    },

    // 伪造Windows注册表输出
    spoofWindowsRegistryOutput: function (output) {
      if (typeof output !== "string") {
        Logger.debug(
          "SYSTEM_INTERCEPTOR",
          "Windows注册表输出非字符串类型，跳过处理",
          typeof output
        );
        return output;
      }

      Logger.info("SYSTEM_INTERCEPTOR", "开始伪造Windows注册表输出", {
        originalLength: output.length,
      });

      let result = output;
      let modificationsCount = 0;

      // 替换机器GUID
      const guidMatches = result.match(
        /(MachineGuid\s+REG_SZ\s+)\{[0-9A-Fa-f-]+\}/g
      );
      if (guidMatches) {
        result = result.replace(
          /(MachineGuid\s+REG_SZ\s+)\{[0-9A-Fa-f-]+\}/g,
          `$1{${this.fakeGUID}}`
        );
        modificationsCount += guidMatches.length;
        Logger.debug(
          "SYSTEM_INTERCEPTOR",
          `替换机器GUID ${guidMatches.length}处`
        );
      }

      // 替换产品ID
      const productIdMatches = result.match(
        /(ProductId\s+REG_SZ\s+)[A-Z0-9\-]+/g
      );
      if (productIdMatches) {
        result = result.replace(
          /(ProductId\s+REG_SZ\s+)[A-Z0-9\-]+/g,
          `$1${this.fakeProductId}`
        );
        modificationsCount += productIdMatches.length;
        Logger.debug(
          "SYSTEM_INTERCEPTOR",
          `替换产品ID ${productIdMatches.length}处`
        );
      }

      // 替换BIOS信息
      const biosMatches = result.match(/(BIOSVersion\s+REG_SZ\s+).+/g);
      if (biosMatches) {
        result = result.replace(
          /(BIOSVersion\s+REG_SZ\s+).+/g,
          `$1FAKE_BIOS_${_._secureRandom(100, 999)}`
        );
        modificationsCount += biosMatches.length;
        Logger.debug(
          "SYSTEM_INTERCEPTOR",
          `替换BIOS信息 ${biosMatches.length}处`
        );
      }

      Logger.info("SYSTEM_INTERCEPTOR", "Windows注册表输出伪造完成", {
        modificationsCount: modificationsCount,
        resultLength: result.length,
      });

      return result;
    },

    // 伪造Linux系统信息
    spoofLinuxSystemInfo: function (output) {
      if (typeof output !== "string") {
        Logger.debug(
          "SYSTEM_INTERCEPTOR",
          "Linux系统信息输出非字符串类型，跳过处理",
          typeof output
        );
        return output;
      }

      Logger.info("SYSTEM_INTERCEPTOR", "开始伪造Linux系统信息输出", {
        originalLength: output.length,
      });

      let result = output;
      let modificationsCount = 0;

      // 替换机器ID
      const machineIdMatches = result.match(/[0-9a-f]{32}/g);
      if (machineIdMatches) {
        result = result.replace(
          /[0-9a-f]{32}/g,
          fakeDataGenerator.generateUUID().replace(/-/g, "")
        );
        modificationsCount += machineIdMatches.length;
        Logger.debug(
          "SYSTEM_INTERCEPTOR",
          `替换机器ID ${machineIdMatches.length}处`
        );
      }

      // 替换MAC地址
      const macMatches = result.match(/([0-9a-f]{2}:){5}[0-9a-f]{2}/gi);
      if (macMatches) {
        result = result.replace(
          /([0-9a-f]{2}:){5}[0-9a-f]{2}/gi,
          Array.from({ length: 6 }, () =>
            _._secureRandom(0, 255).toString(16).padStart(2, "0")
          ).join(":")
        );
        modificationsCount += macMatches.length;
        Logger.debug(
          "SYSTEM_INTERCEPTOR",
          `替换MAC地址 ${macMatches.length}处`
        );
      }

      Logger.info("SYSTEM_INTERCEPTOR", "Linux系统信息输出伪造完成", {
        modificationsCount: modificationsCount,
        resultLength: result.length,
      });

      return result;
    },
  };

  Logger.debug("SYSTEM_INTERCEPTOR", "系统信息拦截器初始化完成", {
    fakeUUID: Logger.sanitizeData(systemInterceptor.fakeUUID),
    fakeSerial: systemInterceptor.fakeSerial,
    fakeMacAddress: systemInterceptor.fakeMacAddress,
  });

  // 拦截child_process模块
  Logger.info("CHILD_PROCESS_INTERCEPTOR", "开始劫持child_process模块");
  const originalRequire2 = require;
  require = function (moduleName) {
    const module = originalRequire2.apply(this, arguments);

    try {
      if (moduleName === "child_process") {
        Logger.info("CHILD_PROCESS_INTERCEPTOR", "成功劫持child_process模块");
        const originalExec = module.exec;
        const originalExecSync = module.execSync;
        // 注意：originalSpawn 暂未使用，但保留以备将来扩展
        const originalSpawn = module.spawn;

        // 拦截exec方法
        module.exec = function (command, options, callback) {
          try {
            if (typeof command === "string") {
              Logger.debug(
                "CHILD_PROCESS_INTERCEPTOR",
                "拦截exec命令",
                Logger.sanitizeData(command)
              );

              // 检查是否为Git命令
              if (command.includes("git ")) {
                Logger.info(
                  "CHILD_PROCESS_INTERCEPTOR",
                  "Git命令被拦截，返回空结果",
                  Logger.sanitizeData(command)
                );
                const fakeCallback = callback || options;
                if (typeof fakeCallback === "function") {
                  setTimeout(() => fakeCallback(null, "", ""), 0);
                }
                return {
                  stdout: {
                    on: function () {
                      Logger.debug(
                        "CHILD_PROCESS_INTERCEPTOR",
                        "伪造Git进程stdout事件监听器"
                      );
                    },
                  },
                  stderr: {
                    on: function () {
                      Logger.debug(
                        "CHILD_PROCESS_INTERCEPTOR",
                        "伪造Git进程stderr事件监听器"
                      );
                    },
                  },
                };
              }

              Logger.debug(
                "CHILD_PROCESS_INTERCEPTOR",
                "exec命令继续执行，准备拦截输出"
              );
              return originalExec.call(
                this,
                command,
                options,
                function (error, stdout, stderr) {
                  try {
                    if (error) {
                      Logger.warn(
                        "CHILD_PROCESS_INTERCEPTOR",
                        "exec命令执行出错",
                        {
                          command: Logger.sanitizeData(command),
                          error: error.message,
                        }
                      );

                      // Git命令返回空结果
                      if (command.includes("git ")) {
                        Logger.info(
                          "CHILD_PROCESS_INTERCEPTOR",
                          "Git命令错误被拦截，返回空结果"
                        );
                        return callback
                          ? callback(null, "", stderr || "")
                          : undefined;
                      }
                      return callback
                        ? callback(error, stdout, stderr)
                        : undefined;
                    }

                    if (stdout && callback) {
                      let spoofedOutput = stdout;
                      let outputModified = false;

                      // macOS系统信息伪造
                      if (command.includes("ioreg")) {
                        Logger.info(
                          "CHILD_PROCESS_INTERCEPTOR",
                          "检测到ioreg命令，开始伪造输出"
                        );
                        spoofedOutput =
                          systemInterceptor.spoofIoregOutput(stdout);
                        outputModified = spoofedOutput !== stdout;
                      }
                      // Windows注册表信息伪造
                      else if (
                        command.includes("REG.exe QUERY") ||
                        command.includes("reg query")
                      ) {
                        Logger.info(
                          "CHILD_PROCESS_INTERCEPTOR",
                          "检测到注册表查询命令，开始伪造输出"
                        );
                        spoofedOutput =
                          systemInterceptor.spoofWindowsRegistryOutput(stdout);
                        outputModified = spoofedOutput !== stdout;
                      }
                      // Linux系统信息伪造
                      else if (
                        command.includes("cat /proc/") ||
                        command.includes("dmidecode")
                      ) {
                        Logger.info(
                          "CHILD_PROCESS_INTERCEPTOR",
                          "检测到Linux系统信息命令，开始伪造输出"
                        );
                        spoofedOutput =
                          systemInterceptor.spoofLinuxSystemInfo(stdout);
                        outputModified = spoofedOutput !== stdout;
                      }
                      // Git命令输出清空
                      else if (command.includes("git ")) {
                        Logger.info(
                          "CHILD_PROCESS_INTERCEPTOR",
                          "Git命令输出被清空"
                        );
                        spoofedOutput = "";
                        outputModified = true;
                      }

                      if (outputModified) {
                        Logger.info(
                          "CHILD_PROCESS_INTERCEPTOR",
                          "命令输出已被修改",
                          {
                            command: Logger.sanitizeData(command),
                            originalLength: stdout.length,
                            modifiedLength: spoofedOutput.length,
                          }
                        );
                      } else {
                        Logger.debug(
                          "CHILD_PROCESS_INTERCEPTOR",
                          "命令输出未被修改",
                          Logger.sanitizeData(command)
                        );
                      }

                      callback(null, spoofedOutput, stderr);
                    } else if (callback) {
                      Logger.debug(
                        "CHILD_PROCESS_INTERCEPTOR",
                        "exec命令无输出或无回调"
                      );
                      callback(null, stdout, stderr);
                    }
                  } catch (err) {
                    Logger.error(
                      "CHILD_PROCESS_INTERCEPTOR",
                      "exec回调处理异常",
                      err.message
                    );
                    if (callback) callback(error || err, stdout, stderr);
                  }
                }
              );
            }
          } catch (err) {
            Logger.error(
              "CHILD_PROCESS_INTERCEPTOR",
              "exec方法拦截异常",
              err.message
            );
          }

          return originalExec.apply(this, arguments);
        };

        // 拦截execSync方法
        module.execSync = function (command, options) {
          try {
            if (typeof command === "string") {
              Logger.debug(
                "CHILD_PROCESS_INTERCEPTOR",
                "拦截execSync命令",
                Logger.sanitizeData(command)
              );

              // Git命令返回空字符串
              if (command.includes("git ")) {
                Logger.info(
                  "CHILD_PROCESS_INTERCEPTOR",
                  "Git execSync命令被拦截，返回空字符串",
                  Logger.sanitizeData(command)
                );
                return "";
              }

              Logger.debug("CHILD_PROCESS_INTERCEPTOR", "execSync命令继续执行");
              const result = originalExecSync.call(this, command, options);

              if (typeof result === "string") {
                let spoofedResult = result;
                let resultModified = false;

                // 系统信息伪造
                if (command.includes("ioreg")) {
                  Logger.info(
                    "CHILD_PROCESS_INTERCEPTOR",
                    "检测到ioreg execSync命令，开始伪造输出"
                  );
                  spoofedResult = systemInterceptor.spoofIoregOutput(result);
                  resultModified = spoofedResult !== result;
                } else if (
                  command.includes("REG.exe QUERY") ||
                  command.includes("reg query")
                ) {
                  Logger.info(
                    "CHILD_PROCESS_INTERCEPTOR",
                    "检测到注册表查询execSync命令，开始伪造输出"
                  );
                  spoofedResult =
                    systemInterceptor.spoofWindowsRegistryOutput(result);
                  resultModified = spoofedResult !== result;
                } else if (
                  command.includes("cat /proc/") ||
                  command.includes("dmidecode")
                ) {
                  Logger.info(
                    "CHILD_PROCESS_INTERCEPTOR",
                    "检测到Linux系统信息execSync命令，开始伪造输出"
                  );
                  spoofedResult =
                    systemInterceptor.spoofLinuxSystemInfo(result);
                  resultModified = spoofedResult !== result;
                }

                if (resultModified) {
                  Logger.info(
                    "CHILD_PROCESS_INTERCEPTOR",
                    "execSync命令输出已被修改",
                    {
                      command: Logger.sanitizeData(command),
                      originalLength: result.length,
                      modifiedLength: spoofedResult.length,
                    }
                  );
                } else {
                  Logger.debug(
                    "CHILD_PROCESS_INTERCEPTOR",
                    "execSync命令输出未被修改",
                    Logger.sanitizeData(command)
                  );
                }

                return spoofedResult;
              }

              Logger.debug(
                "CHILD_PROCESS_INTERCEPTOR",
                "execSync命令返回非字符串结果",
                typeof result
              );
              return result;
            }
          } catch (err) {
            Logger.warn("CHILD_PROCESS_INTERCEPTOR", "execSync方法执行异常", {
              command: Logger.sanitizeData(command),
              error: err.message,
            });

            // 静默处理错误，返回空字符串
            if (command && command.includes("git ")) {
              Logger.info(
                "CHILD_PROCESS_INTERCEPTOR",
                "Git execSync命令异常被拦截，返回空字符串"
              );
              return "";
            }
          }

          return originalExecSync.apply(this, arguments);
        };
      } else {
        Logger.debug(
          "CHILD_PROCESS_INTERCEPTOR",
          `模块${moduleName}未在拦截列表中，直接返回`
        );
      }
    } catch (error) {
      Logger.error(
        "CHILD_PROCESS_INTERCEPTOR",
        `模块${moduleName}劫持失败`,
        error.message
      );
    }

    return module;
  };

  Logger.info("CHILD_PROCESS_INTERCEPTOR", "child_process模块劫持完成");
  Logger.info("SYSTEM", "所有拦截系统初始化完成，开始监控网络请求和系统调用");
})(),
