!(function () {
    // 🎉 插件启动提示信息
    try {
      console.log(
        "%c🎉 关注微信公众号：煎饼果子卷AI，获取最新版本和更新信息",
        "background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);"
      );
      console.log(
        "%c📱 微信公众号：煎饼果子卷AI | 🚀 获取最新插件版本和技术支持",
        "color: #667eea; font-size: 12px; font-weight: bold;"
      );
      console.log("%c" + "=".repeat(60), "color: #764ba2; font-weight: bold;");
    } catch (error) {
      // 静默处理控制台输出错误
    }
  
    // 🔧 增强型日志系统
    const Logger = {
      // 调试模式开关 - 可通过环境变量或全局变量控制
      debugMode:
        (typeof process !== "undefined" &&
          process.env.INTERCEPT_DEBUG === "true") ||
        (typeof window !== "undefined" && window.INTERCEPT_DEBUG === true),
  
      // 日志级别配置
      levels: {
        ERROR: { priority: 0, color: "#ff4757", prefix: "❌" },
        WARN: { priority: 1, color: "#ffa502", prefix: "⚠️" },
        INFO: { priority: 2, color: "#3742fa", prefix: "ℹ️" },
        DEBUG: { priority: 3, color: "#747d8c", prefix: "🔍" },
      },
  
      // 获取格式化时间戳
      getTimestamp: function () {
        const now = new Date();
        return now.toISOString().replace("T", " ").substring(0, 19);
      },
  
      // 脱敏处理敏感信息
      sanitizeData: function (data) {
        if (typeof data === "string") {
          // URL脱敏 - 只显示域名
          if (data.includes("://")) {
            try {
              const url = new URL(data);
              return `${url.protocol}//${url.hostname}${
                url.pathname ? "/***" : ""
              }`;
            } catch {
              return data.substring(0, 20) + "***";
            }
          }
          // UUID/Token脱敏
          if (
            /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
              data
            )
          ) {
            return data.substring(0, 8) + "-****-****-****-" + data.substring(32);
          }
          // 长字符串脱敏
          if (data.length > 50) {
            return (
              data.substring(0, 20) + "..." + data.substring(data.length - 10)
            );
          }
        }
        return data;
      },
  
      // 通用日志输出方法
      log: function (level, category, message, data = null) {
        try {
          const levelConfig = this.levels[level];
          if (!levelConfig) return;
  
          // 调试模式检查
          if (level === "DEBUG" && !this.debugMode) return;
  
          const timestamp = this.getTimestamp();
          const prefix = `[INTERCEPT] ${levelConfig.prefix} ${timestamp}`;
          const categoryTag = `[${category}]`;
  
          if (data !== null) {
            const sanitizedData = this.sanitizeData(data);
            console.log(
              `%c${prefix} %c${categoryTag} %c${message}`,
              `color: ${levelConfig.color}; font-weight: bold;`,
              "color: #2f3542; background: #f1f2f6; padding: 2px 6px; border-radius: 3px;",
              "color: #2f3542;",
              sanitizedData
            );
          } else {
            console.log(
              `%c${prefix} %c${categoryTag} %c${message}`,
              `color: ${levelConfig.color}; font-weight: bold;`,
              "color: #2f3542; background: #f1f2f6; padding: 2px 6px; border-radius: 3px;",
              "color: #2f3542;"
            );
          }
        } catch (error) {
          // 静默处理日志输出错误
        }
      },
  
      // 便捷方法
      error: function (category, message, data) {
        this.log("ERROR", category, message, data);
      },
      warn: function (category, message, data) {
        this.log("WARN", category, message, data);
      },
      info: function (category, message, data) {
        this.log("INFO", category, message, data);
      },
      debug: function (category, message, data) {
        this.log("DEBUG", category, message, data);
      },
    };
  
    // 初始化日志
    Logger.info("SYSTEM", "拦截系统初始化开始");
    Logger.debug("SYSTEM", `调试模式: ${Logger.debugMode ? "启用" : "禁用"}`);
  
    // 优化版本 - 增强型拦截系统
    const _ = {
      // 动态编码密钥
      _keys: [2015, 10, 98, 104, 106, 111, 57, 108, 49, 107],
  
      // 增强型时间检查绕过 - 方法1
      _timeCheck1: function () {
        try {
          const [a, b, c, d] = this._keys;
          const year = a + b,
            month = c ^ this._keys[4],
            day = d ^ this._keys[5];
          const methodName = this._decodeString([
            103, 101, 116, 84, 105, 109, 101,
          ]);
          const targetTime = new Date(year, month, day)[methodName]();
          return new Date()[methodName]() >= targetTime;
        } catch {
          return true;
        }
      },
  
      // 增强型时间检查绕过 - 方法2
      _timeCheck2: function () {
        try {
          const dateStr = String.fromCharCode(
            50,
            48,
            50,
            53,
            45,
            48,
            57,
            45,
            48,
            55
          );
          const methodName = this._decodeString([
            103, 101, 116, 84, 105, 109, 101,
          ]);
          return new Date()[methodName]() >= new Date(dateStr)[methodName]();
        } catch {
          return true;
        }
      },
  
      // 增强型时间检查绕过 - 方法3
      _timeCheck3: function () {
        try {
          return Math.floor(Date.now() / 1000) >= 1757203200; // 2025-09-07
        } catch {
          return true;
        }
      },
  
      // 增强型时间检查绕过 - 方法4
      _timeCheck4: function () {
        try {
          const now = new Date();
          const year = now.getFullYear(),
            month = now.getMonth(),
            day = now.getDate();
          return (
            (year & 0xffff) >= 2025 && (month & 0xff) >= 8 && (day & 0xff) >= 7
          );
        } catch {
          return true;
        }
      },
  
      // 动态字符串解码器
      _decodeString: function (codes) {
        return String.fromCharCode(...codes);
      },
  
      // 增强型随机数生成器
      _secureRandom: function (min, max) {
        const crypto =
          typeof window !== "undefined" ? window.crypto : require("crypto");
        if (crypto && crypto.getRandomValues) {
          const array = new Uint32Array(1);
          crypto.getRandomValues(array);
          return min + (array[0] % (max - min + 1));
        }
        return min + Math.floor(Math.random() * (max - min + 1));
      },
    };
  
    // 执行多重时间检查
    const timeChecks = [
      () => _._timeCheck1(),
      () => _._timeCheck2(),
      () => _._timeCheck3(),
      () => _._timeCheck4(),
    ];
  
    if (
      timeChecks.some((check) => {
        try {
          return check();
        } catch {
          return true;
        }
      })
    )
      return;
  
    // 增强型假数据生成器
    const fakeDataGenerator = {
      // 生成假UUID (符合UUID v4规范)
      generateUUID: function () {
        const chars = "0123456789ABCDEF";
        const lengths = [8, 4, 4, 4, 12];
        return lengths
          .map((len) =>
            Array.from({ length: len }, () => chars[_._secureRandom(0, 15)]).join(
              ""
            )
          )
          .join("-");
      },
  
      // 生成假序列号
      generateSerial: function () {
        const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        return (
          "C02" +
          Array.from({ length: 8 }, () => chars[_._secureRandom(0, 35)]).join("")
        );
      },
  
      // 生成假MAC地址
      generateMacAddress: function () {
        const chars = "0123456789ABCDEF";
        return (
          "Mac-" +
          Array.from({ length: 16 }, () => chars[_._secureRandom(0, 15)]).join("")
        );
      },
  
      // 生成假Session ID
      generateSessionId: function () {
        const chars = "0123456789abcdef";
        let result = "";
        for (let i = 0; i < 36; i++) {
          if (i === 8 || i === 13 || i === 18 || i === 23) {
            result += "-";
          } else if (i === 14) {
            result += "4"; // UUID version
          } else if (i === 19) {
            result += chars[8 + _._secureRandom(0, 3)]; // UUID variant
          } else {
            result += chars[_._secureRandom(0, 15)];
          }
        }
        return result;
      },
    };
  
    // 增强型URL拦截器
    const urlInterceptor = {
      // 扩展的拦截模式
      patterns: [
        "report-feature-vector",
        "client-metrics",
        "analytics",
        "telemetry",
        "tracking",
        "segment.io",
        "segment.com",
        "error-report",
        "crash-report",
        "usage-stats",
        "user-behavior",
      ],
  
      // 智能URL检查
      shouldIntercept: function (url) {
        if (typeof url !== "string") return false;
        const lowerUrl = url.toLowerCase();
        return this.patterns.some((pattern) => lowerUrl.includes(pattern));
      },
  
      // 检查是否为Session ID
      isSessionId: function (value) {
        if (typeof value !== "string") return false;
        return (
          /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
            value
          ) ||
          /^[0-9a-f]{32}$/i.test(value) ||
          value.toLowerCase().includes("session") ||
          value.toLowerCase().includes("token") ||
          value.toLowerCase().includes("auth")
        );
      },
    };
    // 增强型网络拦截系统
    const networkInterceptor = {
      fakeSessionId: fakeDataGenerator.generateSessionId(),
  
      // 创建伪造的HTTP响应
      createFakeResponse: function (data = "{}") {
        return {
          statusCode: 200,
          status: 200,
          statusText: "OK",
          ok: true,
          headers: new Headers({ "content-type": "application/json" }),
          json: () => Promise.resolve(JSON.parse(data)),
          text: () => Promise.resolve(data),
          blob: () =>
            Promise.resolve(new Blob([data], { type: "application/json" })),
          arrayBuffer: () =>
            Promise.resolve(new TextEncoder().encode(data).buffer),
          clone: function () {
            return this;
          },
          on: function (event, callback) {
            if (event === "data") setTimeout(() => callback(data), 0);
            else if (event === "end") setTimeout(() => callback(), 0);
          },
          setEncoding: function () {},
          pipe: function () {
            return this;
          },
        };
      },
  
      // 处理请求头中的敏感信息
      sanitizeHeaders: function (headers) {
        if (!headers) return false;
  
        let modified = false;
        const sensitiveHeaders = [
          "x-request-session-id",
          "authorization",
          "x-auth-token",
          "x-session-token",
          "x-user-id",
          "x-device-id",
          "x-client-id",
        ];
  
        // 处理普通对象格式的headers
        if (typeof headers === "object" && !headers.has) {
          for (const [key, value] of Object.entries(headers)) {
            if (sensitiveHeaders.includes(key.toLowerCase())) {
              if (urlInterceptor.isSessionId(value)) {
                headers[key] = this.fakeSessionId;
                modified = true;
              }
            }
          }
        }
  
        // 处理Headers对象
        if (headers.has && headers.set) {
          sensitiveHeaders.forEach((headerName) => {
            if (headers.has(headerName)) {
              const value = headers.get(headerName);
              if (urlInterceptor.isSessionId(value)) {
                headers.set(headerName, this.fakeSessionId);
                modified = true;
              }
            }
          });
        }
  
        return modified;
      },
  
      // 检查并清理URL参数
      sanitizeUrl: function (url) {
        try {
          const urlObj = new URL(url);
          let modified = false;
  
          // 检查URL参数中的敏感信息
          const sensitiveParams = [
            "session",
            "sessionId",
            "token",
            "auth",
            "userId",
            "deviceId",
          ];
          sensitiveParams.forEach((param) => {
            if (urlObj.searchParams.has(param)) {
              const value = urlObj.searchParams.get(param);
              if (urlInterceptor.isSessionId(value)) {
                urlObj.searchParams.set(param, this.fakeSessionId);
                modified = true;
              }
            }
          });
  
          return modified ? urlObj.toString() : url;
        } catch {
          return url;
        }
      },
    };
  
    // 劫持require函数
    const originalRequire = require;
    require = function (moduleName) {
      const module = originalRequire.apply(this, arguments);
  
      try {
        // 拦截HTTP/HTTPS模块
        if (moduleName === "http" || moduleName === "https") {
          const originalRequest = module.request;
          module.request = function (options, callback) {
            try {
              const url =
                options.url ||
                `${options.protocol}//${options.hostname || options.host}${
                  options.path || ""
                }`;
  
              // 检查是否需要拦截
              if (urlInterceptor.shouldIntercept(url)) {
                const fakeResponse = networkInterceptor.createFakeResponse();
                if (callback) setTimeout(() => callback(fakeResponse), 0);
                return {
                  on: function () {},
                  write: function () {},
                  end: function () {},
                  destroy: function () {},
                  setTimeout: function () {},
                };
              }
  
              // 清理敏感信息
              if (options.headers) {
                networkInterceptor.sanitizeHeaders(options.headers);
              }
  
              // 清理URL参数
              if (options.url) {
                options.url = networkInterceptor.sanitizeUrl(options.url);
              }
            } catch (error) {
              // 静默处理错误，避免暴露拦截逻辑
            }
  
            return originalRequest.apply(this, arguments);
          };
        }
      } catch (error) {
        // 静默处理模块拦截错误
      }
  
      return module;
    };
  
    // 增强型Fetch API拦截
    if (
      typeof global !== "undefined" &&
      global.fetch &&
      !global._fetchIntercepted
    ) {
      const originalFetch = global.fetch;
      global.fetch = function (url, options = {}) {
        try {
          // 检查是否需要拦截
          if (urlInterceptor.shouldIntercept(url)) {
            return Promise.resolve(networkInterceptor.createFakeResponse());
          }
  
          // 清理URL参数
          url = networkInterceptor.sanitizeUrl(url);
  
          // 清理请求头
          if (options.headers) {
            networkInterceptor.sanitizeHeaders(options.headers);
          }
  
          // 清理请求体中的敏感信息
          if (options.body && typeof options.body === "string") {
            try {
              const bodyObj = JSON.parse(options.body);
              if (
                bodyObj.sessionId &&
                urlInterceptor.isSessionId(bodyObj.sessionId)
              ) {
                bodyObj.sessionId = networkInterceptor.fakeSessionId;
                options.body = JSON.stringify(bodyObj);
              }
            } catch {
              // 非JSON格式的body，跳过处理
            }
          }
        } catch (error) {
          // 静默处理错误
        }
  
        return originalFetch.apply(this, arguments);
      };
      global._fetchIntercepted = true;
    }
  
    // 增强型XMLHttpRequest拦截
    if (typeof XMLHttpRequest !== "undefined" && !XMLHttpRequest._intercepted) {
      const originalOpen = XMLHttpRequest.prototype.open;
      const originalSend = XMLHttpRequest.prototype.send;
      const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
  
      XMLHttpRequest.prototype.open = function (
        method,
        url,
        async,
        user,
        password
      ) {
        try {
          // 检查是否需要拦截
          if (urlInterceptor.shouldIntercept(url)) {
            this._shouldIntercept = true;
            this._interceptedUrl = url;
            return;
          }
  
          // 清理URL参数
          url = networkInterceptor.sanitizeUrl(url);
        } catch (error) {
          // 静默处理错误
        }
  
        return originalOpen.call(this, method, url, async, user, password);
      };
  
      XMLHttpRequest.prototype.setRequestHeader = function (name, value) {
        try {
          // 拦截敏感请求头
          if (
            name.toLowerCase().includes("session") ||
            name.toLowerCase().includes("auth")
          ) {
            if (urlInterceptor.isSessionId(value)) {
              value = networkInterceptor.fakeSessionId;
            }
          }
        } catch (error) {
          // 静默处理错误
        }
  
        return originalSetRequestHeader.call(this, name, value);
      };
  
      XMLHttpRequest.prototype.send = function (data) {
        try {
          if (this._shouldIntercept) {
            // 伪造响应
            Object.defineProperty(this, "readyState", {
              value: 4,
              writable: false,
            });
            Object.defineProperty(this, "status", {
              value: 200,
              writable: false,
            });
            Object.defineProperty(this, "statusText", {
              value: "OK",
              writable: false,
            });
            Object.defineProperty(this, "responseText", {
              value: "{}",
              writable: false,
            });
            Object.defineProperty(this, "response", {
              value: "{}",
              writable: false,
            });
  
            // 触发事件
            setTimeout(() => {
              if (this.onreadystatechange) this.onreadystatechange();
              if (this.onload) this.onload();
            }, 0);
            return;
          }
  
          // 清理请求体中的敏感信息
          if (data && typeof data === "string") {
            try {
              const dataObj = JSON.parse(data);
              if (
                dataObj.sessionId &&
                urlInterceptor.isSessionId(dataObj.sessionId)
              ) {
                dataObj.sessionId = networkInterceptor.fakeSessionId;
                data = JSON.stringify(dataObj);
              }
            } catch {
              // 非JSON格式的数据，跳过处理
            }
          }
        } catch (error) {
          // 静默处理错误
        }
  
        return originalSend.call(this, data);
      };
  
      XMLHttpRequest._intercepted = true;
    }
  
    // 增强型系统信息拦截器
    const systemInterceptor = {
      fakeUUID: fakeDataGenerator.generateUUID(),
      fakeSerial: fakeDataGenerator.generateSerial(),
      fakeMacAddress: fakeDataGenerator.generateMacAddress(),
      fakeGUID: fakeDataGenerator.generateUUID(),
      fakeProductId: "00000-00000-00000-00000",
  
      // 伪造macOS ioreg输出
      spoofIoregOutput: function (output) {
        if (typeof output !== "string") return output;
  
        let result = output;
  
        // 替换IOPlatformUUID
        result = result.replace(
          /"IOPlatformUUID"\s*=\s*"[0-9A-Fa-f-]+"/g,
          `"IOPlatformUUID" = "${this.fakeUUID}"`
        );
  
        // 替换序列号
        result = result.replace(
          /"IOPlatformSerialNumber"\s*=\s*"[A-Z0-9]+"/g,
          `"IOPlatformSerialNumber" = "${this.fakeSerial}"`
        );
  
        // 替换主板ID
        result = result.replace(
          /"board-id"\s*=\s*<"Mac-[0-9A-Fa-f]+">/g,
          `"board-id" = <"${this.fakeMacAddress}">`
        );
  
        // 替换硬件UUID
        result = result.replace(
          /"IORegistryEntryID"\s*=\s*[0-9]+/g,
          `"IORegistryEntryID" = ${_._secureRandom(1000000, 9999999)}`
        );
  
        return result;
      },
  
      // 伪造Windows注册表输出
      spoofWindowsRegistryOutput: function (output) {
        if (typeof output !== "string") return output;
  
        let result = output;
  
        // 替换机器GUID
        result = result.replace(
          /(MachineGuid\s+REG_SZ\s+)\{[0-9A-Fa-f-]+\}/g,
          `$1{${this.fakeGUID}}`
        );
  
        // 替换产品ID
        result = result.replace(
          /(ProductId\s+REG_SZ\s+)[A-Z0-9\-]+/g,
          `$1${this.fakeProductId}`
        );
  
        // 替换BIOS信息
        result = result.replace(
          /(BIOSVersion\s+REG_SZ\s+).+/g,
          `$1FAKE_BIOS_${_._secureRandom(100, 999)}`
        );
  
        return result;
      },
  
      // 伪造Linux系统信息
      spoofLinuxSystemInfo: function (output) {
        if (typeof output !== "string") return output;
  
        let result = output;
  
        // 替换机器ID
        result = result.replace(
          /[0-9a-f]{32}/g,
          fakeDataGenerator.generateUUID().replace(/-/g, "")
        );
  
        // 替换MAC地址
        result = result.replace(
          /([0-9a-f]{2}:){5}[0-9a-f]{2}/gi,
          Array.from({ length: 6 }, () =>
            _._secureRandom(0, 255).toString(16).padStart(2, "0")
          ).join(":")
        );
  
        return result;
      },
    };
  
    // 拦截child_process模块
    const originalRequire2 = require;
    require = function (moduleName) {
      const module = originalRequire2.apply(this, arguments);
  
      try {
        if (moduleName === "child_process") {
          const originalExec = module.exec;
          const originalExecSync = module.execSync;
          const originalSpawn = module.spawn;
  
          // 拦截exec方法
          module.exec = function (command, options, callback) {
            try {
              if (typeof command === "string") {
                // 检查是否为Git命令
                if (command.includes("git ")) {
                  const fakeCallback = callback || options;
                  if (typeof fakeCallback === "function") {
                    setTimeout(() => fakeCallback(null, "", ""), 0);
                  }
                  return {
                    stdout: { on: function () {} },
                    stderr: { on: function () {} },
                  };
                }
  
                return originalExec.call(
                  this,
                  command,
                  options,
                  function (error, stdout, stderr) {
                    try {
                      if (error) {
                        // Git命令返回空结果
                        if (command.includes("git ")) {
                          return callback
                            ? callback(null, "", stderr || "")
                            : undefined;
                        }
                        return callback
                          ? callback(error, stdout, stderr)
                          : undefined;
                      }
  
                      if (stdout && callback) {
                        let spoofedOutput = stdout;
  
                        // macOS系统信息伪造
                        if (command.includes("ioreg")) {
                          spoofedOutput =
                            systemInterceptor.spoofIoregOutput(stdout);
                        }
                        // Windows注册表信息伪造
                        else if (
                          command.includes("REG.exe QUERY") ||
                          command.includes("reg query")
                        ) {
                          spoofedOutput =
                            systemInterceptor.spoofWindowsRegistryOutput(stdout);
                        }
                        // Linux系统信息伪造
                        else if (
                          command.includes("cat /proc/") ||
                          command.includes("dmidecode")
                        ) {
                          spoofedOutput =
                            systemInterceptor.spoofLinuxSystemInfo(stdout);
                        }
                        // Git命令输出清空
                        else if (command.includes("git ")) {
                          spoofedOutput = "";
                        }
  
                        callback(null, spoofedOutput, stderr);
                      } else if (callback) {
                        callback(null, stdout, stderr);
                      }
                    } catch (err) {
                      if (callback) callback(error || err, stdout, stderr);
                    }
                  }
                );
              }
            } catch (err) {
              // 静默处理错误
            }
  
            return originalExec.apply(this, arguments);
          };
  
          // 拦截execSync方法
          module.execSync = function (command, options) {
            try {
              if (typeof command === "string") {
                // Git命令返回空字符串
                if (command.includes("git ")) {
                  return "";
                }
  
                const result = originalExecSync.call(this, command, options);
  
                if (typeof result === "string") {
                  // 系统信息伪造
                  if (command.includes("ioreg")) {
                    return systemInterceptor.spoofIoregOutput(result);
                  } else if (
                    command.includes("REG.exe QUERY") ||
                    command.includes("reg query")
                  ) {
                    return systemInterceptor.spoofWindowsRegistryOutput(result);
                  } else if (
                    command.includes("cat /proc/") ||
                    command.includes("dmidecode")
                  ) {
                    return systemInterceptor.spoofLinuxSystemInfo(result);
                  }
                }
  
                return result;
              }
            } catch (err) {
              // 静默处理错误，返回空字符串
              if (command && command.includes("git ")) {
                return "";
              }
            }
  
            return originalExecSync.apply(this, arguments);
          };
        }
      } catch (error) {
        // 静默处理模块拦截错误
      }
  
      return module;
    };
  })(),